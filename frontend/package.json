{"name": "@quarterback/frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 8080", "start:local": "next start -p 3000", "lint": "next lint"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@fast-csv/parse": "^5.0.0", "@headlessui/react": "2.1.0", "@heroicons/react": "2.2.0", "@quarterback/types": "workspace:*", "@quarterback/util": "workspace:*", "@react-pdf/layout": "3.12.1", "@react-pdf/renderer": "3.4.4", "@react-pdf/types": "2.5.0", "@react-spring/web": "9.7.3", "@sentry/nextjs": "8.13.0", "@tailwindcss/forms": "0.5.7", "@visx/axis": "3.10.1", "@visx/curve": "3.3.0", "@visx/event": "3.3.0", "@visx/glyph": "3.3.0", "@visx/gradient": "3.3.0", "@visx/grid": "3.5.0", "@visx/group": "3.3.0", "@visx/legend": "3.5.0", "@visx/marker": "3.5.0", "@visx/responsive": "3.10.2", "@visx/scale": "3.5.0", "@visx/shape": "3.5.0", "@visx/stats": "3.5.0", "@visx/tooltip": "3.3.0", "@visx/vendor": "3.5.0", "@vvo/tzdb": "^6.161.0", "classnames": "2.5.1", "date-fns": "4.1.0", "date-fns-tz": "^3.2.0", "export-to-csv": "1.3.0", "next": "14.2.0", "nextjs-cors": "2.2.0", "posthog-js": "1.155.0", "react": "18", "react-day-picker": "9.0.0-rc.2", "react-dom": "18", "react-pdf-tailwind": "2.3.0", "react-wordcloud": "^1.2.7", "remark": "15.0.1", "remark-html": "16.0.1", "remark-parse": "11.0.0", "sass": "1.77.8", "stream-browserify": "^3.0.0", "supertokens-auth-react": "0.39.1", "supertokens-node": "18.0.0", "swr": "2.2.5", "unified": "11.0.5", "unist-util-visit": "5.0.0", "zod": "3.23.8"}, "devDependencies": {"@types/node": "20", "@types/react": "18", "@types/react-dom": "18", "eslint": "8", "eslint-config-next": "14.2.0", "eslint-config-prettier": "9.1.0", "postcss": "8", "tailwindcss": "3.4.1", "typescript": "5"}}