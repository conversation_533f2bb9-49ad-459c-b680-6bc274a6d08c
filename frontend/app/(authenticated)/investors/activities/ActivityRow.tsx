import { isBroadcast } from '@/api/hooks/useBroadcasts';
import SentimentIndicator from '@/app/(authenticated)/investors/activities/SentimentIndicator';
import ActivityFormatIndicator from '@/components/ActivityFormatIndicator';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import Checkbox from '@/components/ui/Checkbox';
import { formatWithTimeZone } from '@/util/date';
import useActivitySummary from '@/util/useActivitySummary';
import { ArrowTurnDownRightIcon, FlagIcon } from '@heroicons/react/20/solid';
import { Activity } from '@quarterback/types';
import classNames from 'classnames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';
import { useActivitiesLayout } from './ActivityLayoutContext';
import activityFormat from '@/util/activityFormat';

export default function ActivityRow({
    activity,
    className,
    selectedActivities,
    setSelectedActivities
}: {
    activity: Activity;
    className?: string;
    selectedActivities: Array<string>;
    setSelectedActivities: React.Dispatch<React.SetStateAction<string[]>>;
}) {
    const pathname = usePathname();
    const searchParams = useSearchParams();

    const { setIsRightPanelExpanded } = useActivitiesLayout();
    const router = useRouter();
    const { title, description, author, engagement } = useActivitySummary(activity);

    const broadcast = useMemo(() => {
        return isBroadcast(activity);
    }, [activity]);

    const truncatedAuthor = useMemo(() => {
        if (author === 'Australian Securities Exchange') return 'ASX';
        else if (author && (author?.length ?? 0) > 25) {
            return author.substring(0, 25) + '…';
        } else {
            return author;
        }
    }, [author]);

    function handleClick() {
        setIsRightPanelExpanded(true)
        router.push(
            `/investors/activities/${activity.id}${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`
        );
    }

    const showThreadIcon = useMemo(() => {
        return activity.type === 'hotcopper' && activity.url.includes('page-');
    }, [activity]);

    const priceSensitive = useMemo(() => {
        if (activity.type === 'asx-announcement' && activity.priceSensitive)
            return <span className="text-red-500">$</span>;
        return null;
    }, [activity]);

    const handleCheckboxClick = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation();
        setSelectedActivities(prev => {
            if (e.target.checked) {
                return activity.id ? [...prev, activity.id] : prev;
            } else {
                return prev.filter(id => id !== activity.id);
            }
        });
    }
    const activeRow = pathname === `/investors/activities/${activity.id}`;
    return (
        <tr
            key={activity.id}
            className={classNames(className, 'cursor-pointer group ',
                {
                    'bg-purple-100 hover:bg-purple-100': activeRow,
                    'bg-purple-50  hover:bg-purple-100': !activeRow,
                },)}
            onClick={handleClick}>
            <td
                className={classNames(
                    {
                        "bg-purple-100": activeRow,
                        "bg-white": !activeRow,

                    },
                    "whitespace-nowrap text-sm px-2 group-hover:bg-purple-100")}
            >
                <Checkbox
                    onClick={(e) => e.stopPropagation()}
                    onChange={handleCheckboxClick}
                    checked={activity.id ? selectedActivities.includes(activity.id) : false}
                />
            </td>
            <td className={classNames(
                {
                    "bg-purple-100": activeRow,
                    "bg-white": !activeRow,

                },
                "whitespace-nowrap text-sm px-2 group-hover:bg-purple-100")}>
                <div className="flex items-center gap-x-2">
                    <ActivitySourceIcon
                        className="rounded-md size-5 object-cover"
                        activity={activity}
                    />
                    <span className="text-xs font-medium">{truncatedAuthor}</span>
                </div>
            </td>

            <td className="text-gray-500 px-4 overflow-hidden break-all text-ellipsis border border-gray-200  group-hover:bg-purple-100">
                <div className="text-sm font-medium flex items-center gap-x-2">
                    {showThreadIcon ? (
                        <ArrowTurnDownRightIcon className="size-5 text-indigo-800" />
                    ) : null}
                    {title ? (
                        <>
                            <span className="text-gray-900 text-ellipsis overflow-hidden line-clamp-1 break-all">
                                {title}
                            </span>
                            {priceSensitive}
                        </>
                    ) : null}
                    {description ? (
                        <span className="mt-1 text-xs flex-1 text-ellipsis overflow-hidden line-clamp-1 break-all">
                            {description}
                        </span>
                    ) : null}
                </div>
            </td>
            <td className="whitespace-nowrap py-3 text-xs text-gray-500 sm:pl-3 border border-gray-200 group-hover:bg-purple-100">
                {formatWithTimeZone(activity.posted, 'HH:mm')}
            </td>
            <td className="relative whitespace-nowrap text-right text-sm font-medium sm:pr-3 border border-gray-200">
                {activity.sentiment && (
                    <SentimentIndicator sentiment={activity.sentiment} />
                )}
            </td>
            <td className="whitespace-nowrap py-2 text-xs text-gray-500 sm:pl-3 text-right pr-3 border border-gray-200 group-hover:bg-purple-100">
                {
                    <ActivityFormatIndicator
                        activity={activityFormat(activity)}
                    />
                }
            </td>
        </tr >
    );
}
