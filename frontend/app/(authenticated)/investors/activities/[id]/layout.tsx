'use client';

import useActivities from '@/api/hooks/useActivities';
import useRisk from '@/api/hooks/useRisk';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import ActivitiesManagerContext from '@/app/(authenticated)/investors/activities/ActivitiesManagerContext';
import { useOrganisation } from '@/components/OrganisationProvider';
import { groupBy } from '@quarterback/util';
import { startOfDay, sub } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useActivitiesLayout } from '../ActivityLayoutContext';
import classNames from 'classnames';
import { XCircleIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export default function ActivitiesLayout({
    children,
    params
}: {
    children: React.ReactNode;
    params: { id: string };
}) {
    const organisation = useOrganisation();
    const searchParams = useSearchParams();
    const { isRightPanelExpanded, setIsRightPanelExpanded } = useActivitiesLayout();

    const [range] = useState<DateRange>({
        from: startOfDay(
            searchParams.has('from')
                ? new Date(searchParams.get('from')!)
                : sub(new Date(), { months: 1 })
        ),
        to: startOfDay(
            searchParams.has('to') ? new Date(searchParams.get('to')!) : new Date()
        )
    });

    const { data: activities = [] } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!,
        { archived: false }
    );

    const { data: risk } = useRisk(organisation?.selected?.entity, {
        symbol: 'AXJO',
        exchange: 'ASX'
    });

    const { data: indexSeries } = useTimeSeries(
        { symbol: 'AXJO', exchange: 'ASX' },
        range.from!,
        range.to!
    );

    const { data: timeSeries } = useTimeSeries(
        organisation?.selected?.entity,
        range.from!,
        range.to!
    );

    const timeSeriesByDate = useMemo(
        () =>
            groupBy(timeSeries?.values ?? [], (it) =>
                formatInTimeZone(new Date(it.datetime), 'Australia/Sydney', 'yyyy-MM-dd')
            ),
        [timeSeries]
    );

    const indexSeriesByDate = useMemo(
        () =>
            groupBy(indexSeries?.values ?? [], (it) =>
                formatInTimeZone(new Date(it.datetime), 'Australia/Sydney', 'yyyy-MM-dd')
            ),
        [indexSeries]
    );

    const activitiesByDay = useMemo(
        () =>
            groupBy(activities, (it) =>
                formatInTimeZone(it.posted!, 'Australia/Sydney', 'yyyy-MM-dd')
            ),
        [activities]
    );

    // Automatically expand the right panel when viewing an activity detail
    useEffect(() => {
        setIsRightPanelExpanded(true);
    }, [setIsRightPanelExpanded]);

    function closeRightPanel() {
        setIsRightPanelExpanded(false);
    }
    return (
        <>
            <span className="absolute top-[15px] z-20">
                <Link href="/investors/activities">
                    <XCircleIcon className="w-6 h-6" onClick={closeRightPanel} />
                </Link>
            </span>

            <div
                className={classNames(
                    isRightPanelExpanded ? 'w-[32rem]' : 'w-0',
                    'top-[60px] bottom-0 right-0 overflow-y-scroll fixed border-l border-gray-300'
                )}
                style={{ boxShadow: '-4px 0 6px -1px rgba(0,0,0,0.1)' }}>
                <ActivitiesManagerContext.Provider
                    value={{
                        timeSeriesByDate,
                        indexSeriesByDate,
                        risk,
                        activitiesByDay
                    }}>
                    {children}
                </ActivitiesManagerContext.Provider>
            </div>
        </>
    );
}
