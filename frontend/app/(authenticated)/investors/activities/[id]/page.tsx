'use client';

import { Bars4Icon, ChatBubbleLeftEllipsisIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import DetailTab from "./DetailTab";
import CommentTab from "./CommentTab";

const TABS = {
    DETAILS: 'details',
    COMMENTS: 'comments'
}

export default function ActivityPage({ params }: { params: { id: string } }) {
    const [activeTab, setActiveTab] = useState(TABS.DETAILS);

    return (
        <div className="flex flex-col  h-full">
            <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
                <div className="flex space-x-6 px-4 text-sm font-medium h-[52px]">
                    <button
                        onClick={() => setActiveTab(TABS.DETAILS)}
                        className={`py-2 border-b-2 ${activeTab === TABS.DETAILS
                            ? "border-black text-black"
                            : "border-transparent text-gray-500 hover:text-black"
                            }`}
                    >
                        <div className="flex items-center space-x-1">
                            <Bars4Icon className='h-4 w-4' />
                            <span>Details</span>
                        </div>
                    </button>

                    <button
                        onClick={() => setActiveTab(TABS.COMMENTS)}
                        className={`py-2 border-b-2 ${activeTab === TABS.COMMENTS
                            ? "border-black text-black"
                            : "border-transparent text-gray-500 hover:text-black"
                            }`}
                    >
                        <div className="flex items-center space-x-1">
                            <ChatBubbleLeftEllipsisIcon className='h-4 w-4' />
                            <span>Comments</span>
                        </div>
                    </button>
                </div>
            </div>


            {activeTab === TABS.DETAILS ? (
                <DetailTab params={params} />
            ) : (
                <CommentTab params={params} />
            )}

            {/* Sticky Footer */}
            {/* {activeTab === TABS.DETAILS && <div className="bg-white border-t border-gray-200 sticky bottom-0 px-4 py-3 text-sm text-gray-700 flex justify-between items-center">
                <span>Footer</span>

            </div>} */}
        </div>
    )
}