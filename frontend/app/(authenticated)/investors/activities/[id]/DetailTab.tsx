'use client';

import useActivityReadMutation from '@/api/hooks/mutations/useActivityReadMutation';
import useArchiveActivityMutation from '@/api/hooks/mutations/useArchiveActivityMutation';
import useFlagActivityMutation from '@/api/hooks/mutations/useFlagActivityMutation';
import useActivity from '@/api/hooks/useActivity';
import { isChatter } from '@/api/hooks/useChatter';
import ActivityFilesList from '@/components/ActivityFilesList';
import ActivitiesManagerContext from '@/app/(authenticated)/investors/activities/ActivitiesManagerContext';
import ActivityAction from '@/components/ActivityAction';
import ActivityDetailsCard from '@/components/ActivityDetailsCard';
import ActivityThread from '@/components/ActivityThread';
import ChangeIndicator from '@/components/ChangeIndicator';
import { useOrganisation } from '@/components/OrganisationProvider';
import { formatWithTimeZone } from '@/util/date';
import { useSentimentBands } from '@/util/sentiment';
import useActivitySummary from '@/util/useActivitySummary';
import {
    ArrowsUpDownIcon,
    ChevronDownIcon,
    ChevronRightIcon,
    CurrencyDollarIcon,
    EnvelopeIcon,
    EnvelopeOpenIcon,
    FlagIcon as FlagIconOutline,
    HashtagIcon,
    LinkIcon,
    PercentBadgeIcon,
    TrashIcon as TrashIconOutline
} from '@heroicons/react/24/outline';
import {
    FlagIcon as FlagIconSolid,
    TrashIcon as TrashIconSolid
} from '@heroicons/react/24/solid';
import { useContext, useMemo, useState } from 'react';
import { useActivityFiles } from '@/api/hooks/useActivityFiles';
import classNames from 'classnames';

export default function DetailTab({ params }: { params: { id: string } }) {
    const [isSharePerformanceExpanded, setIsSharePerformanceExpanded] = useState(true)
    const organisation = useOrganisation();

    const { data: activity } = useActivity(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        params.id
    );

    const { files } = useActivityFiles(params.id);

    const { timeSeriesByDate, indexSeriesByDate, activitiesByDay, risk } = useContext(
        ActivitiesManagerContext
    );
    const { url } = useActivitySummary(activity);

    const day = useMemo(() => {
        return activity && formatWithTimeZone(activity.posted, 'yyyy-MM-dd');
    }, [activity]);

    const quote = useMemo(() => {
        if (day !== undefined && timeSeriesByDate !== undefined) {
            return timeSeriesByDate?.[day]?.[0];
        } else {
            return undefined;
        }
    }, [day, timeSeriesByDate]);

    const previousDayQuote = useMemo(() => {
        const timeSeries = Object.values(timeSeriesByDate ?? {})
            .flatMap((it) => it[0])
            ?.reverse();

        const dayIndex = timeSeries.findIndex((it) => it.datetime === day);
        return timeSeries[dayIndex + 1] ?? undefined;
    }, [day, timeSeriesByDate]);

    const changeIndicator = useMemo(() => {
        if (quote && previousDayQuote && previousDayQuote.close > 0) {
            const logReturn = Math.log(quote.close / previousDayQuote.close);
            const percentage = logReturn * 100;
            const sign = percentage > 0 ? '+' : percentage < 0 ? '-' : '';
            return (
                <ChangeIndicator
                    value={logReturn}
                    label={`${Math.abs(logReturn).toFixed(2)} (${sign}${Math.abs(
                        percentage
                    ).toFixed(2)}%)`}
                />
            );
        } else {
            return undefined;
        }
    }, [quote, previousDayQuote]);

    const index = useMemo(() => {
        if (day !== undefined && indexSeriesByDate !== undefined) {
            return indexSeriesByDate?.[day]?.[0];
        } else {
            return undefined;
        }
    }, [day, indexSeriesByDate]);

    const previousIndex = useMemo(() => {
        const indexSeries = Object.values(indexSeriesByDate ?? {})
            .flatMap((it) => it[0])
            ?.reverse();
        const dayIndex = indexSeries.findIndex((it) => it.datetime === day);
        return indexSeries[dayIndex + 1] ?? undefined;
    }, [day, indexSeriesByDate]);

    const abnormal = useMemo(() => {
        if (
            quote &&
            previousDayQuote &&
            index &&
            previousIndex &&
            risk &&
            previousDayQuote.close > 0 &&
            previousIndex.close > 0
        ) {
            const stockLogReturn = Math.log(quote.close / previousDayQuote.close);
            const indexLogReturn = Math.log(index.close / previousIndex.close);
            const expectedReturn = risk.intercept + risk.slope * indexLogReturn;
            const abnormalReturn = stockLogReturn - expectedReturn;
            return abnormalReturn;
        } else {
            return undefined;
        }
    }, [quote, previousDayQuote, index, risk, previousIndex]);

    const todaysChatter = useMemo(() => {
        if (day && activitiesByDay && activitiesByDay[day]) {
            return activitiesByDay[day].filter((it) => isChatter(it));
        } else {
            return [];
        }
    }, [day, activitiesByDay]);

    const sentimentBands = useSentimentBands(todaysChatter);

    // TODO: add followers & mailchimp subscribers
    const stats: Array<[string, JSX.Element | string, JSX.Element | undefined, JSX.Element | undefined]> = [
        ...(quote !== undefined
            ? [
                ['Share price (Open)', `$${quote.open.toFixed(4)}`, undefined, <CurrencyDollarIcon key="share-price-open" className="h-4 w-4 text-gray-500" />] as [
                    string,
                    string,
                    undefined,
                    JSX.Element
                ],
                [
                    'Day range',
                    `$${quote.low.toFixed(4)} - $${quote.high.toFixed(4)}`,
                    undefined,
                    <ArrowsUpDownIcon key="day-range" className="h-4 w-4 text-gray-500" />
                ] as [string, string, undefined, JSX.Element],
                [
                    'Share price (close)',
                    `$${quote.close.toFixed(4)}`,
                    changeIndicator,
                    <CurrencyDollarIcon key="share-price-close" className="h-4 w-4 text-gray-500" />
                ] as [string, string, undefined, JSX.Element],
                ['Volume traded',
                    `${quote.volume.toLocaleString()}`,
                    undefined,
                    <HashtagIcon key="volume-traded" className="h-4 w-4 text-gray-500" />
                ] as [
                    string,
                    string,
                    undefined, JSX.Element
                ]
            ]
            : []),
        ...(abnormal !== undefined
            ? [
                [
                    'Abnormal return',
                    <ChangeIndicator
                        value={abnormal}
                        key={abnormal}
                        label={`${(Math.abs(abnormal) * 100).toFixed(2)}%`}
                    />,
                    undefined,
                    <PercentBadgeIcon key="volume-traded" className="h-4 w-4 text-gray-500" />

                ] as [string, JSX.Element, undefined, JSX.Element]
            ]
            : [])
    ];

    const threadId: string | undefined = useMemo(() => {
        switch (activity?.type) {
            case 'hotcopper':
                return activity?.thread?.thread ?? undefined;

            case 'reddit':
            case 'redditComment':
                return activity.post;

            case 'tweet':
            case 'linkedIn':
                return activity?.thread ?? undefined;

            default:
                return undefined;
        }
    }, [activity]);

    const { read, unread } = useActivityReadMutation();
    const { flag, unflag } = useFlagActivityMutation(organisation.selected?.organisation);
    const { archive, unarchive } = useArchiveActivityMutation(
        organisation.selected?.organisation
    );

    const collapseSharePerformanceCard = () => setIsSharePerformanceExpanded(false);
    const expandSharePerformanceCard = () => setIsSharePerformanceExpanded(true);
    if (!activity) {
        return (
            <div className="p-4 flex flex-col gap-y-6 bg-gray-100 flex-1 h-full vh-100" />
        );
    }

    return (
        <>
            <div className="flex-1 overflow-y-auto overflow-x-hidden px-4 py-4 bg-gray-50">

                <div className="flex flex-col justify-between h-full">
                    <div className='flex flex-col flex-1'>
                        {/* SHARE PERFORMACE CARD */}
                        <div className={classNames(isSharePerformanceExpanded ? 'flex flex-col' : 'inline-flex', "mt-1 gap-y-2 border-b border-gray-200 pb-2")}>
                            {activity?.posted && (
                                isSharePerformanceExpanded ? <div className="text-sm font-semibold flex justify-between">
                                    <span className='flex items-center'><ChevronDownIcon className='h-4 w-4 mr-2 cursor-pointer' onClick={collapseSharePerformanceCard} /> Share Performance</span>
                                    <span>{formatWithTimeZone(activity.posted, 'd MMMM yyyy')}</span>
                                </div>
                                    :
                                    <div className='bg-gray-200 rounded-md px-2' onClick={expandSharePerformanceCard}>
                                        < span className='flex items-center'><ChevronRightIcon className='h-4 w-4 mr-2 cursor-pointer' /> Performance</span>
                                    </div>

                            )}
                            {isSharePerformanceExpanded && <>
                                <div className="overflow-hidden flex rounded-md">
                                    <div
                                        className="h-2 bg-red-500"
                                        style={{ width: `${(sentimentBands[0] * 100).toFixed(2)}%` }}
                                    />
                                    <div
                                        className="h-2 bg-yellow-300"
                                        style={{ width: `${(sentimentBands[1] * 100).toFixed(2)}%` }}
                                    />
                                    <div
                                        className="h-2 bg-slate-300"
                                        style={{ width: `${(sentimentBands[2] * 100).toFixed(2)}%` }}
                                    />
                                    <div
                                        className="h-2 bg-green-400"
                                        style={{ width: `${(sentimentBands[3] * 100).toFixed(2)}%` }}
                                    />
                                </div>
                                <dl>
                                    {stats.length > 0 ? (
                                        stats.map(([label, value, changeIndicator, icon]) => (
                                            <div key={label} className="py-2 flex sm:col-span-1">
                                                {/* Left Column: Icon + Label */}
                                                <div className="text-sm font-medium text-gray-900 flex items-center gap-2 w-1/2">
                                                    {icon && <div className="text-gray-500">{icon}</div>}
                                                    <dt className="text-sm font-medium text-gray-900">{label}</dt>
                                                </div>

                                                {/* Right Column: Value + Optional Change Indicator */}
                                                <dd className="w-1/2 text-sm text-gray-700 flex items-center gap-2">
                                                    <span>{value}</span>
                                                    {changeIndicator}
                                                </dd>
                                            </div>
                                        ))
                                    ) : (
                                        <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                            <dt className="text-sm font-medium text-gray-900">
                                                Market closed
                                            </dt>
                                        </div>
                                    )}
                                </dl>
                            </>}
                        </div>

                        {/* activity buttons */}
                        <div className="flex flex-col gap-y-1 mt-6">
                            <div className="flex flex-row justify-between items-center">
                                <dt className="text-sm font-semibold text-gray-500">Activity</dt>
                                <dt className="text-sm font-semibold text-gray-500">
                                    <div className="flex items-center gap-x-2 ">
                                        <ActivityAction
                                            icon={activity?.read ? EnvelopeOpenIcon : EnvelopeIcon}
                                            tooltip={`Mark as ${activity?.read ? 'unread' : 'read'}`}
                                            onClick={async () => {
                                                if (activity?.read) {
                                                    await unread(
                                                        new URLSearchParams({ id: params.id })
                                                    );
                                                } else {
                                                    await read([{ activity: params.id }]);
                                                }
                                            }}
                                        />
                                        <ActivityAction
                                            tooltip={`${activity?.flagged ? 'Un-flag' : 'Flag'} activity`}
                                            icon={activity?.flagged ? FlagIconSolid : FlagIconOutline}
                                            onClick={async () => {
                                                if (activity?.flagged) {
                                                    await unflag(
                                                        new URLSearchParams({ id: params.id })
                                                    );
                                                } else {
                                                    await flag([{ activity: params.id }]);
                                                }
                                            }}
                                        />
                                        <ActivityAction
                                            tooltip={`${activity?.archived ? 'Un-archive' : 'Archive'} activity`}
                                            icon={
                                                activity?.archived ? TrashIconSolid : TrashIconOutline
                                            }
                                            onClick={async () => {
                                                if (activity?.archived) {
                                                    await unarchive(
                                                        new URLSearchParams({ id: params.id })
                                                    );
                                                } else {
                                                    await archive([{ activity: params.id }]);
                                                }
                                            }}
                                        />
                                    </div>
                                </dt>
                            </div>
                        </div>
                        {/* activity detail card */}
                        <div className="mt-1 flex flex-col ">
                            {!threadId ? (
                                <ActivityDetailsCard activity={activity} imgClassNames="-mx-8" />
                            ) : null}

                            {threadId ? (
                                <ActivityThread threadId={threadId} selectedActivity={activity?.id} />
                            ) : null}
                        </div>

                        {
                            files?.length > 0 && (
                                <>
                                    <dt className="text-sm font-semibold text-gray-500 mt-6">
                                        Attachments
                                    </dt>
                                    <div className="mt-3 border border-gray-200 bg-white p-4 rounded-md">
                                        <ActivityFilesList files={files} />
                                    </div>
                                </>
                            )
                        }
                    </div >


                </div >


            </div>
            {/* Sticky Footer */}
            <div className="bg-white border-t border-gray-200 sticky bottom-0 px-4 py-3 text-sm text-gray-700 flex justify-between items-center">
                <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:underline flex items-center text-sm font-medium text-indigo-600 gap-x-1 ">
                    <LinkIcon className="size-4" />
                    <span>Open in new tab</span>
                </a>

            </div>
        </>
    );
}
