import { Activity, ListedEntity } from '@quarterback/types';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import React, { useState } from 'react';
import classNames from 'classnames';
import GroupHeader from '@/app/(authenticated)/investors/activities/GroupHeader';
import ActivityRow from '@/app/(authenticated)/investors/activities/ActivityRow';

export default function ActivityGroup({
    group,
    entity,
    quote,
    index,
    risk
}: {
    group: { name: string; datetime: Date; activities: Array<Activity> };
    entity: ListedEntity | undefined;
    quote: {
        current: TimeSeriesQuote | undefined;
        previous: TimeSeriesQuote | undefined;
    };
    index: {
        current: TimeSeriesQuote | undefined;
        previous: TimeSeriesQuote | undefined;
    };
    risk: { slope: number; intercept: number; r2: number } | undefined;
}) {
    const [selectedActivities, setSelectedActivities] = useState<Array<string>>([])


    return (
        <>
            <GroupHeader
                group={group}
                quote={quote}
            />
            {
                group.activities.map((activity, index) => (
                    <ActivityRow
                        key={activity.id}
                        activity={activity}
                        className={classNames(
                            index === 0 ? 'border-gray-300' : 'border-gray-200',
                            'border-t'
                        )}
                        selectedActivities={selectedActivities}
                        setSelectedActivities={setSelectedActivities}
                    />
                ))
            }
        </>
    );
}
