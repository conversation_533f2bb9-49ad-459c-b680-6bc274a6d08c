import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import { discreteSentiment, DiscreteSentiment, sentimentScore } from '@/util/sentiment';
import useActivitySummary from '@/util/useActivitySummary';
import { Activity } from '@quarterback/types';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

interface SentimentActivityListProps {
    activities: Activity[];
    sentimentType: 'negative' | 'positive';
}

const SENTIMENT_COLORS = {
    [DiscreteSentiment.NEGATIVE]: '#f87171',
    [DiscreteSentiment.LACKING]: '#facc15',
    [DiscreteSentiment.NEUTRAL]: '#e2e8f0',
    [DiscreteSentiment.POSITIVE]: '#4ade80'
};

export default function SentimentActivityList({
    activities,
    sentimentType
}: SentimentActivityListProps) {
    const filteredAndSortedActivities = useMemo(() => {
        return activities
            .filter((activity) => activity.sentiment)
            .map((activity) => ({
                activity,
                sentimentScore: sentimentScore(activity.sentiment!),
                discreteSentiment: discreteSentiment(sentimentScore(activity.sentiment!))
            }))
            .filter(({ discreteSentiment: sentiment }) => {
                if (sentimentType === 'negative') {
                    return (
                        sentiment === DiscreteSentiment.NEGATIVE ||
                        sentiment === DiscreteSentiment.LACKING
                    );
                } else {
                    return sentiment === DiscreteSentiment.POSITIVE;
                }
            })
            .sort((a, b) => {
                const dateA = a.activity.posted
                    ? new Date(a.activity.posted).getTime()
                    : 0;
                const dateB = b.activity.posted
                    ? new Date(b.activity.posted).getTime()
                    : 0;
                return dateB - dateA;
            })
            .slice(0, 5);
    }, [activities, sentimentType]);

    if (filteredAndSortedActivities.length === 0) {
        return (
            <div className="flex items-center justify-center py-8 text-gray-500">
                No {sentimentType} sentiment activities found
            </div>
        );
    }

    return (
        <div className="overflow-hidden">
            <div className="overflow-x-auto">
                <table className="min-w-full">
                    <thead>
                        <tr className="border-b border-gray-200 bg-gray-100">
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Source
                            </th>
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-4">
                                Body
                            </th>
                            <th className="text-right text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Sentiment
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {filteredAndSortedActivities.map((activity) => (
                            <ActivityRow
                                key={activity.activity.id}
                                activity={activity.activity}
                                sentimentScore={activity.sentimentScore}
                                discreteSentiment={activity.discreteSentiment!}
                            />
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}

interface ActivityRowProps {
    activity: Activity;
    sentimentScore: number;
    discreteSentiment: DiscreteSentiment;
}

function ActivityRow({ activity, sentimentScore, discreteSentiment }: ActivityRowProps) {
    const { author, description, title } = useActivitySummary(activity);
    const searchParams = useSearchParams();
    const router = useRouter();

    function handleClick() {
        router.push(
            `/investors/activities/${activity.id}${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`
        );
    }

    const sentimentColor = SENTIMENT_COLORS[discreteSentiment];

    return (
        <tr className="hover:bg-gray-50" onClick={handleClick} role="button">
            <td className="py-3 px-2">
                <div className="flex items-center gap-x-2 min-w-0">
                    <ActivitySourceIcon
                        className="rounded-md size-5 object-cover flex-shrink-0"
                        activity={activity}
                    />
                    <span className="text-xs font-medium text-gray-900 truncate">
                        {author || 'Unknown'}
                    </span>
                </div>
            </td>
            <td className="py-3 px-4">
                <div className="text-sm text-gray-900 line-clamp-2 break-words">
                    {title || description}
                </div>
            </td>
            <td className="py-3 px-2 text-right">
                <span className="text-sm font-medium" style={{ color: sentimentColor }}>
                    {sentimentScore.toFixed(2)}
                </span>
            </td>
        </tr>
    );
}
