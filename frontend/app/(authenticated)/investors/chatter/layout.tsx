'use client';

import ContentCard from '@/components/cards/ContentCard';
import ActivityPie from '@/components/charts/activity-pie';
import HotcopperDisclosurePie from '@/components/charts/HotcopperDisclorePie';
import HotcopperSentimentPie from '@/components/charts/HotcopperSentimentPie';
import SentimentDisclosure from '@/components/charts/sentimentPie';
import SentimentOverTime from '@/components/charts/sentimentovertime';
import SourceOverTime from '@/components/charts/sourceOverTime';
import SourceSentiments from '@/components/charts/SourceSentiments';
import DateRangePicker from '@/components/DateRangePicker';
import Initials from '@/components/Initials';
import useChatterStats from '@/hooks/useChatterStats';
import InitialColors from '@/util/InitialColors';
import { ComputerDesktopIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { startOfDay, sub } from 'date-fns';
import { useSearchParams } from 'next/navigation';
import { useMemo, useState } from 'react';
import { DateRange } from 'react-day-picker';
import TopEngagedPosts from '../activities/components/TopEngagedPosts';
import People from '../activities/components/People';
import Threads from '../activities/components/Threads';
import SentimentChatterTabs from '@/app/(authenticated)/investors/activities/components/SentimentChatterTabs';
import WordCloud from '@/components/wordcloud';
import { AIInsight } from '../dashboard/Sidebar';
import Topics from '@/components/charts/Topics';

export default function ActivitiesLayout({ children }: React.PropsWithChildren) {
    const searchParams = useSearchParams();

    const [range, setRange] = useState<DateRange>({
        from: startOfDay(
            searchParams.has('from')
                ? new Date(searchParams.get('from')!)
                : sub(new Date(), {
                      months: 1
                  })
        ),
        to: startOfDay(
            searchParams.has('to') ? new Date(searchParams.get('to')!) : new Date()
        )
    });

    const {
        organisation,
        activities,
        activitiesCount,
        peopleCount,
        newPeople,
        averageSentiment,
        loading,
        wordCloud,
        wordCloudLoading,
        summary,
        summaryLoading,
        topics,
        topicsLoading
    } = useChatterStats(range);

    const twitterActivities = useMemo(() => {
        return activities.filter((activity) => activity.type === 'tweet');
    }, [activities]);

    const linkedInActivities = useMemo(
        () => activities.filter((activity) => activity.type === 'linkedIn'),
        [activities]
    );

    const hotcopperActivities = useMemo(
        () => activities.filter((activity) => activity.type === 'hotcopper'),
        [activities]
    );

    return (
        <>
            <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
                <div className="flex flex-row px-4 py-2 gap-x-2 justify-between items-center">
                    <DateRangePicker range={range} setRange={setRange} required />

                    {/* Add activity button goes here */}
                </div>
            </div>
            <div className="overflow-y-auto flex flex-col gap-y-6 px-4 py-4">
                <div className="flex flex-1 gap-x-2">
                    <div className="flex w-3/4 flex-1 ">
                        <div className="flex flex-1 flex-col  gap-y-2">
                            <div className="flex flex-1 gap-x-2">
                                <div className="w-2/4 flex-1 ">
                                    <div className="flex flex-col gap-y-2">
                                        <div className="flex gap-x-2">
                                            <div className="w-1/4">
                                                <ContentCard
                                                    title="Activities"
                                                    className="h-full "
                                                    loading={loading}
                                                    initialBadge={{
                                                        color: InitialColors.ACTIVITY
                                                    }}>
                                                    <span className="text-3xl font-normal">
                                                        {activitiesCount}
                                                    </span>
                                                </ContentCard>
                                            </div>
                                            <div className="w-1/4">
                                                <ContentCard
                                                    title="People"
                                                    className="h-full"
                                                    loading={loading}
                                                    initialBadge={{
                                                        color: InitialColors.PEOPLE
                                                    }}>
                                                    <span className="text-3xl font-normal">
                                                        {peopleCount}
                                                    </span>
                                                </ContentCard>
                                            </div>
                                            <div className="w-1/4">
                                                <ContentCard
                                                    className="h-full"
                                                    loading={loading}
                                                    title={
                                                        <>
                                                            <div className="flex gap-x-2 items-center">
                                                                <span>New</span>
                                                                <Initials
                                                                    name="People"
                                                                    color={
                                                                        InitialColors.PEOPLE
                                                                    }
                                                                />
                                                                <span>People</span>
                                                            </div>
                                                        </>
                                                    }>
                                                    <span className="text-3xl font-normal">
                                                        {newPeople}
                                                    </span>
                                                </ContentCard>
                                            </div>
                                            <div className="w-1/4">
                                                <ContentCard
                                                    className="h-full"
                                                    title="Average Sentiment"
                                                    loading={loading}
                                                    initialBadge={{
                                                        hide: true
                                                    }}>
                                                    <span className="text-3xl font-normal">
                                                        {averageSentiment.toFixed(2)}
                                                    </span>
                                                </ContentCard>
                                            </div>
                                        </div>
                                        <div>
                                            <ContentCard
                                                className="h-full"
                                                loading={loading}
                                                title={
                                                    <div className="flex gap-x-2 items-center">
                                                        <ComputerDesktopIcon className="size-4" />
                                                        <span>Source over time</span>
                                                    </div>
                                                }
                                                initialBadge={{
                                                    hide: true
                                                }}>
                                                <div className="w-full overflow-hidden">
                                                    <SourceOverTime
                                                        activities={activities}
                                                        height={310}
                                                    />
                                                </div>
                                            </ContentCard>
                                        </div>
                                    </div>
                                </div>
                                <div className="w-1/4">
                                    <ContentCard
                                        className="h-full"
                                        loading={loading}
                                        title={
                                            <>
                                                <div className="flex gap-x-1 items-center">
                                                    <Initials
                                                        name="Activities"
                                                        color={InitialColors.ACTIVITY}
                                                    />
                                                    <span>Activities</span>
                                                    <span>VS Source</span>
                                                </div>
                                            </>
                                        }>
                                        <ActivityPie
                                            entity={organisation.selected?.entity}
                                            activities={activities}
                                            groupBy="source"
                                        />
                                    </ContentCard>
                                </div>
                            </div>
                            <div className="flex flex-1 gap-x-2">
                                <div className="flex-1">
                                    <ContentCard
                                        className="h-full"
                                        loading={loading || topicsLoading}
                                        title="Topics"
                                        initialBadge={{ hide: true }}>
                                        <Topics topics={topics || []} />
                                    </ContentCard>
                                </div>
                                <div className="flex-1">
                                    <ContentCard
                                        className="h-full"
                                        loading={loading || wordCloudLoading}
                                        title="Word cloud"
                                        initialBadge={{ hide: true }}>
                                        <WordCloud wordCloud={wordCloud || []} />
                                    </ContentCard>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="w-1/4">
                        <AIInsight
                            markdown={summary}
                            loading={summaryLoading}
                            selected={organisation.selected!}
                        />
                    </div>
                </div>
                <div className="flex flex-1 flex-col gap-y-4">
                    <span className="text-xl font-medium">Sentiment</span>
                    <div className="flex flex-1 gap-x-2">
                        <div className="flex flex-1 gap-x-2 w-1/3">
                            <div className="w-1/2">
                                <ContentCard
                                    className="h-full"
                                    title="Setiment brakdown"
                                    loading={loading}
                                    initialBadge={{
                                        hide: true
                                    }}>
                                    <SentimentDisclosure activities={activities} />
                                </ContentCard>
                            </div>
                            <div className="w-1/2">
                                <ContentCard
                                    className="h-full"
                                    loading={loading}
                                    title="Setiment VS Source"
                                    initialBadge={{
                                        hide: true
                                    }}>
                                    <SourceSentiments activities={activities} />
                                </ContentCard>
                            </div>
                        </div>
                        <div className="w-1/3">
                            <SentimentChatterTabs
                                activities={activities}
                                loading={loading}
                                className="h-full"
                            />
                        </div>
                        <div className="w-1/3">
                            <ContentCard
                                className="h-full"
                                title="Sentiment over time"
                                loading={loading}
                                initialBadge={{
                                    hide: true
                                }}>
                                <div className="w-full overflow-hidden">
                                    <SentimentOverTime
                                        activities={activities}
                                        height={300}
                                    />
                                </div>
                            </ContentCard>
                        </div>
                    </div>
                </div>
                <div className="flex flex-1 flex-col gap-y-4">
                    <span className="text-xl font-medium">Hotcopper</span>
                    <div className="flex flex-1 gap-x-2">
                        <div className="flex flex-1 gap-x-2 w-1/3">
                            <div className="w-1/2">
                                <ContentCard
                                    className="h-full"
                                    loading={loading}
                                    title="Disclosure brakdown"
                                    initialBadge={{
                                        hide: true
                                    }}>
                                    <HotcopperDisclosurePie activities={activities} />
                                </ContentCard>
                            </div>
                            <div className="w-1/2">
                                <ContentCard
                                    className="h-full"
                                    title="Sentiment breakdown"
                                    loading={loading}
                                    initialBadge={{
                                        hide: true
                                    }}>
                                    <HotcopperSentimentPie activities={activities} />
                                </ContentCard>
                            </div>
                        </div>
                        <div className="w-1/3">
                            <ContentCard
                                className="h-full"
                                loading={loading}
                                childrenClassName="!mt-0"
                                title="People"
                                initialBadge={{
                                    hide: true
                                }}>
                                <People activities={hotcopperActivities} />
                            </ContentCard>
                        </div>
                        <div className="w-1/3">
                            <ContentCard
                                className="h-full"
                                loading={loading}
                                childrenClassName="!mt-0"
                                title="Threads"
                                initialBadge={{
                                    hide: true
                                }}>
                                <Threads activities={activities} />
                            </ContentCard>
                        </div>
                    </div>
                </div>
                <div className="flex flex-1 flex-col gap-y-4">
                    <span className="text-xl font-medium">X (Twitter)</span>
                    <div className="flex flex-1 gap-x-2">
                        <div className="w-full">
                            <ContentCard
                                className="h-full"
                                childrenClassName="!mt-0"
                                loading={loading}
                                title="People"
                                initialBadge={{
                                    color: InitialColors.PEOPLE
                                }}>
                                <People activities={twitterActivities} />
                            </ContentCard>
                        </div>
                        <div className="w-full">
                            <ContentCard
                                className="h-full"
                                title="Top engaged posts"
                                loading={loading}
                                childrenClassName="!mt-0"
                                initialBadge={{
                                    hide: true
                                }}>
                                <TopEngagedPosts activities={twitterActivities} />
                            </ContentCard>
                        </div>

                        <div className="w-1/2">
                            <ContentCard
                                className="h-full"
                                title="Sentiment breakdown"
                                loading={loading}
                                initialBadge={{
                                    hide: true
                                }}>
                                <SentimentDisclosure activities={twitterActivities} />
                            </ContentCard>
                        </div>
                    </div>
                </div>
                <div className="flex flex-1 flex-col gap-y-4">
                    <span className="text-xl font-medium">LinkedIn</span>
                    <div className="flex flex-1 gap-x-2">
                        <div className="w-full">
                            <ContentCard
                                className="h-full"
                                childrenClassName="!mt-0"
                                loading={loading}
                                title="People"
                                initialBadge={{
                                    color: InitialColors.PEOPLE
                                }}>
                                <People activities={linkedInActivities} />
                            </ContentCard>
                        </div>
                        <div className="w-full">
                            <ContentCard
                                className="h-full"
                                loading={loading}
                                title="Top engaged posts"
                                childrenClassName="!mt-0"
                                initialBadge={{
                                    hide: true
                                }}>
                                <TopEngagedPosts activities={linkedInActivities} />
                            </ContentCard>
                        </div>

                        <div className="w-1/2">
                            <ContentCard
                                className="h-full"
                                loading={loading}
                                title="Sentiment breakdown"
                                initialBadge={{
                                    hide: true
                                }}>
                                <SentimentDisclosure activities={linkedInActivities} />
                            </ContentCard>
                        </div>
                    </div>
                </div>
                {children}
            </div>
        </>
    );
}
