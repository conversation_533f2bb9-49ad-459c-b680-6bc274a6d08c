'use client';

import useActivities from '@/api/hooks/useActivities';
import useBroadcasts from '@/api/hooks/useBroadcasts';
import useChatter from '@/api/hooks/useChatter';
import useFollowers from '@/api/hooks/useFollowers';
import useMedia from '@/api/hooks/useMedia';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import Chatter from '@/app/(authenticated)/investors/dashboard/Chatter';
import Metrics from '@/app/(authenticated)/investors/dashboard/Metrics';
import Sidebar from '@/app/(authenticated)/investors/dashboard/Sidebar';
import DateRangePicker from '@/components/DateRangePicker';
import Initials from '@/components/Initials';
import { useOrganisation } from '@/components/OrganisationProvider';
import ContentCard from '@/components/cards/ContentCard';
import SourceSentiments from '@/components/charts/SourceSentiments';
import ActivityChart from '@/components/charts/activity';
import ActivityPie from '@/components/charts/activity-pie';
import InitialColors from '@/util/InitialColors';
import { ParentSize } from '@visx/responsive';
import { startOfDay, sub } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';

export default function Dashboard() {
    const router = useRouter();

    const [range, setRange] = useState<DateRange>({
        from: startOfDay(sub(new Date(), { months: 1 })),
        to: startOfDay(new Date())
    });

    const organisation = useOrganisation();

    const { data: activities = [] } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: followers = { twitter: [], linkedIn: [] } } = useFollowers(
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: timeSeries } = useTimeSeries(
        organisation?.selected
            ? {
                  symbol: organisation.selected.entity.symbol,
                  exchange: organisation.selected.entity.exchange
              }
            : undefined,
        range.from!,
        range.to!
    );

    const broadcasts = useBroadcasts(activities);
    const chatter = useChatter(activities);
    const media = useMedia(activities);

    function handleActivityChartClick(datetime: string) {
        router.push(`/investors/activities?from=${datetime}&to=${datetime}`);
    }

    return (
        <div>
            <div className="flex items-center border-b border-gray-200 p-4">
                <DateRangePicker range={range} setRange={setRange} required />
            </div>
            <div className="py-4 flex flex-col gap-4">
                <div className="flex lg:flex-row flex-col gap-4 px-4">
                    <div className="flex flex-col flex-1 gap-2">
                        <div className="col-span-8">
                            <Chatter
                                media={media}
                                chatter={chatter}
                                followers={followers}
                                className="p-4 bg-white border rounded-md"
                            />
                        </div>
                        <div className="col-span-8">
                            <ContentCard
                                title={
                                    <div className="flex items-center text-base font-medium gap-2">
                                        <span>Share price vs</span>
                                        <Initials
                                            color={InitialColors.ACTIVITY}
                                            name="Activities"
                                        />
                                        <span>Activities</span>
                                    </div>
                                }
                                className="w-full bg-white p-4 border mt-4 rounded-lg">
                                <div className="h-[32rem]">
                                    <ParentSize>
                                        {({ width, height }) => (
                                            <ActivityChart
                                                width={width}
                                                height={height}
                                                entity={organisation?.selected?.entity}
                                                activities={activities}
                                                followers={followers}
                                                timeSeries={[
                                                    ...(timeSeries?.values ?? [])
                                                ].sort((a, b) =>
                                                    a.datetime < b.datetime ? -1 : 1
                                                )}
                                                onClick={handleActivityChartClick}
                                            />
                                        )}
                                    </ParentSize>
                                </div>
                                <div className="flex justify-center gap-x-4 -mt-6">
                                    <div className="flex gap-x-2 items-center">
                                        <div
                                            className="size-3 rounded-full"
                                            style={{ background: '#56B5A7' }}
                                        />
                                        <span className="text-sm">Publication</span>
                                    </div>
                                    <div className="flex gap-x-2 items-center">
                                        <div
                                            className="size-3 rounded-full"
                                            style={{ background: '#4E7FEE' }}
                                        />
                                        <span className="text-sm">Chatter</span>
                                    </div>
                                    <div className="flex gap-x-2 items-center">
                                        <div
                                            className="size-3 rounded-full"
                                            style={{ background: '#845DEE' }}
                                        />
                                        <span className="text-sm">Broadcast</span>
                                    </div>
                                    <div className="flex gap-x-2 items-center">
                                        <div
                                            className="size-3 rounded-full"
                                            style={{ background: '#DFB649' }}
                                        />
                                        <span className="text-sm">Announcement</span>
                                    </div>
                                    <div className="flex gap-x-2 items-center">
                                        <div
                                            className="size-3 rounded-full"
                                            style={{ background: '#9aa2a7' }}
                                        />
                                        <span className="text-sm">Trade Volume</span>
                                    </div>
                                </div>
                            </ContentCard>
                        </div>
                    </div>
                    <div className="items-stretch w-full lg:w-1/3">
                        <Sidebar />
                    </div>
                </div>
                <div className="flex flex-wrap lg:flex-nowrap  items-stretch gap-4 px-4">
                    <ContentCard className="w-[20%]" title={<>Sentiment vs Source</>}>
                        <SourceSentiments activities={activities} />
                    </ContentCard>

                    <ContentCard
                        className="w-[20%]"
                        title="Activities by Format"
                        initialBadge={{
                            color: InitialColors.ACTIVITY
                        }}>
                        <ActivityPie
                            entity={organisation.selected?.entity}
                            activities={activities}
                            groupBy="format"
                        />
                    </ContentCard>
                    <ContentCard
                        className="w-[20%]"
                        title="Activities by Source"
                        initialBadge={{
                            color: InitialColors.ACTIVITY
                        }}>
                        <ActivityPie
                            entity={organisation.selected?.entity}
                            activities={activities}
                            groupBy="source"
                        />
                    </ContentCard>

                    <Metrics className="w-[40%]" from={range.from!} to={range.to!} />
                </div>
            </div>
        </div>
    );
}
